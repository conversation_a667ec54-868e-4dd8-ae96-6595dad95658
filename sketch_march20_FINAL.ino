#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_BNO055.h>
#include <SPI.h>
#include <SD.h>
#include <HardwareSerial.h>
#include <TimeLib.h>
#include <ArduinoJson.h>


// IMU Pins (I2C)
#define SDA_PIN 8
#define SCL_PIN 9

// SD Card Pins (SPI)
#define SD_MISO 37
#define SD_MOSI 7
#define SD_SCK 36
#define SD_CS 5

// Serial Port for 4G module
#define MODEM_TX 18
#define MODEM_RX 17
HardwareSerial modem(1);

unsigned long baseTimestamp = 0;      // Unix timestamp from SIM at sync time
unsigned long baseMillis = 0;         // millis() value when timestamp was synced
unsigned long lastTimeSync = 0;       // Time of last sync in millis
const unsigned long syncInterval = 2UL * 60UL * 1000UL; // 2 minutes in milliseconds (change *UL to update)

// APN Configuration
const char *APN = "internet.freedommobile.ca";

// Initialize BNO055 IMU
Adafruit_BNO055 bno = Adafruit_BNO055(55, 0x28);

// Collection time seconds (30 seconds by default)
const int collectionTime = 30; //units in seconds
int fileCounter = 1; // Counter for file naming

// File sending to server timing setup
unsigned long lastSendTime = 0;
const unsigned long sendInterval = 5 *60 * 1000; // 5 inutes

void setup() {
    Serial.begin(115200);
    modem.begin(115200, SERIAL_8N1, MODEM_RX, MODEM_TX);
    
    // Initialize I2C for IMU
    Wire.begin(SDA_PIN, SCL_PIN);
    
    Serial.println("Initializing BNO055...");
    if (!bno.begin()) {
        Serial.println("❌ BNO055 not detected. Check wiring!");
        while (1);
    }
    Serial.println("✅ BNO055 detected successfully!");
    bno.setExtCrystalUse(true);
    delay(1000);
    
    // Initialize SPI for SD card
    SPI.begin(SD_SCK, SD_MISO, SD_MOSI, SD_CS);
    if (!SD.begin(SD_CS)) {
        Serial.println("❌ SD Card initialization failed!");
        return;
    }
    Serial.println("✅ SD Card initialized successfully.");

    // Create CSV header if file doesn't exist
    File file = SD.open("/imu_data.csv", FILE_WRITE);
    if (file) {
        if (file.size() == 0) {
            file.println("Timestamp,X,Y,Z");
        }
        file.close();
    }

    // Initialize 4G Connection and send all unsent files at the start
    initializeModem();
    sendAllUnsentFiles();

    // Initialize GPS
    //initializeGPS();

    //Getting timestamp
    if (getNetworkTime()) {
    // Print the baseTimestamp after successful sync
    Serial.println("📍 Initial timestamp: " + String(baseTimestamp));
    } else {
        // If time sync failed
        Serial.println("❌ Failed to sync time.");
    }
}

void loop() {
     if (millis() - lastTimeSync > syncInterval) {
  //  sendAllUnsentFiles();
      Serial.println("Old baseTimestamp: " + String(baseTimestamp));
      getNetworkTime();
    }

    if (millis() - lastSendTime >= sendInterval) {
      Serial.println("SENDING ALL UNSENT FILES...");
        sendAllUnsentFiles();
        lastSendTime = millis();
    }

    unsigned long preTimestamp = getCurrentTimestamp();
    //Serial.println("preTimestamp: ");
    //Serial.println(preTimestamp);
    unsigned long postTimestamp = preTimestamp;

    Serial.println("Making filename...");
    String filename = "/imu_data_" + String(preTimestamp) + String(fileCounter) + ".txt";
    fileCounter++; // Increment for next file
    Serial.println("Filename is: " + filename);
    
    Serial.println("Creating file...");
    File file = SD.open(filename, FILE_WRITE);
    if (!file) {
        Serial.println("Error opening file on SD card.");
        return;
    }

    Serial.println("Making data string...");
    String dataString = "[";
    bool first = true; // To handle json commas properly
    while (postTimestamp - preTimestamp < collectionTime) {
        sensors_event_t event;
        bno.getEvent(&event);
        
        // Get IMU data
        float x = event.orientation.x;
        float y = event.orientation.y;
        float z = event.orientation.z;
        
        // Get timestamp
        //unsigned long timestamp = millis();
        unsigned long currentTimestamp = getCurrentTimestamp();
        postTimestamp = currentTimestamp;
        //Serial.println("postTimestamp: ");
        //Serial.println(postTimestamp);
        //Serial.println("postTimestamp - preTimestamp: ");
        //Serial.println(postTimestamp - preTimestamp);
        
        // Get GPS data
        //String gpsData = getGPSData();
        String gpsData = "gpsData_filler";
        
        // Format data string in CSV format
        //String dataString = String(currentTimestamp) + "," + String(x, 4) + "," + String(y, 4) + "," + String(z, 4) + "," + gpsData;

        //Add comma only after the first item
        if (!first) dataString += ",\n";
        first = false;
        
        // Create JSON object to send
        String dataLine = "{";
        dataLine += "\"timestamp\":" + String(currentTimestamp) + ",";
        dataLine += "\"x\":" + String(x, 4) + ",";
        dataLine += "\"y\":" + String(y, 4) + ",";
        dataLine += "\"z\":" + String(z, 4) + ",";
        dataLine += "\"gps\":\"" + gpsData + "\"}";
        dataString += dataLine;
        Serial.println("Logging: " + dataLine); 
        delay(1000);  // Collect data every second
    }
    dataString += "]";
    Serial.println("Logging: " + dataString); 
    // Write data to file
    file.println(dataString);
    file.close();
    
    Serial.println("Data block saved to SD card.");

    // Send the file over 4G
    //sendFileToServer(filename);
    // Send JSON to server (replace URL if different for JSON endpoint)
//    if (sendJsonToServer(dataString)){
//        markFileAsSent(filename);
//       }
}

// Function to initialize modem and establish network connection
void initializeModem() {
    Serial.println("🔄 Initializing 4G Module...");

    sendATCommand("AT", "OK", 3000);
    sendATCommand("AT+CPIN?", "READY", 5000);
    sendATCommand("AT+CEREG?", "0,1", 5000);
    sendATCommand("AT+CSQ", "OK", 3000);
    sendATCommand("AT+CGATT=1", "OK", 5000);
    sendATCommand("AT+CGDCONT=1,\"IP\",\"" + String(APN) + "\"", "OK", 5000);
    sendATCommand("AT+CGACT=1,1", "OK", 5000);

    Serial.println("🌍 Checking Internet Connectivity...");

    // Test internet by making a GET request to google.com
    if (testInternetConnection()) {
        Serial.println("✅ Internet Connection Established!");
    } else {
        Serial.println("❌ Internet Connection Failed. Check APN or Signal.");
    }
}

bool sendJsonToServer(String json) {
    Serial.println("Sending JSON to server...");

    if (!sendATCommand("AT+HTTPINIT", "OK", 3000)) return false;
    if (!sendATCommand("AT+HTTPPARA=\"URL\",\"http://ac12-142-244-15-144.ngrok-free.app/api/sagitta\"", "OK", 3000)) return false;
    if (!sendATCommand("AT+HTTPPARA=\"CONTENT\",\"application/json\"", "OK", 3000)) return false;

    if (!sendATCommand("AT+HTTPDATA=" + String(json.length()) + ",5000", "DOWNLOAD", 5000)) return false;
    modem.println(json);
    delay(1000);

    if (!sendATCommand("AT+HTTPACTION=1", "200", 7000)) return false; // 200 = success

    if (!sendATCommand("AT+HTTPTERM", "OK", 3000)) return false;

    return true;
}

// Function to send file content via 4G
bool sendFileToServer(String jsonString){

    Serial.println("Sending JSON to server...");
    
    if (!sendATCommand("AT+HTTPINIT", "OK", 3000)) return false;
    if (!sendATCommand("AT+HTTPPARA=\"URL\",\"http://ac12-142-244-15-144.ngrok-free.app/api/sagitta\"", "OK", 3000)) return false;
    if (!sendATCommand("AT+HTTPPARA=\"CONTENT\",\"application/json\"", "OK", 3000)) return false;
    
    if (!sendATCommand("AT+HTTPDATA=" + String(jsonString.length()) + ",10000", "DOWNLOAD", 10000)) return false;
    modem.println(jsonString);
    delay(5000);
    
    if (!sendATCommand("AT+HTTPACTION=1", "200", 7000)) return false; // 200 = success
    
    if (!sendATCommand("AT+HTTPTERM", "OK", 3000)) return false;
    
    return true;
}

// Function to send AT commands and wait for response
bool sendATCommand(String command, String expectedResponse, int timeout) {
    modem.println(command);
    long int time = millis();
    while ((millis() - time) < timeout) {
        while (modem.available()) {
            String response = modem.readString();
            Serial.println("📡 Response: " + response);
            if (response.indexOf(expectedResponse) != -1) {
                return true;
            }
        }
    }
    Serial.println("❌ Timeout/Error on command: " + command);
    return false;
}

// Function to test internet connection
bool testInternetConnection() {
    sendATCommand("AT+HTTPINIT", "OK", 3000);
    sendATCommand("AT+HTTPPARA=\"URL\",\"http://www.google.com\"", "OK", 3000);
    sendATCommand("AT+HTTPACTION=0", "200", 7000);  // 0 = GET request
    
    // Read only 512 bytes from the response
    if (sendATCommand("AT+HTTPREAD=0,512", "OK", 5000)) {
        sendATCommand("AT+HTTPTERM", "OK", 3000);
        return true;
    }
    
    sendATCommand("AT+HTTPTERM", "OK", 3000);
    return false;
}



void initializeGPS() {
    Serial.println("🛰️ Turning on GNSS...");
    sendATCommand("AT+CGNSSPWR=1", "OK", 3000);  // Turn on GNSS

    Serial.println("🔄 Waiting for GPS fix...");
    unsigned long startTime = millis();
    bool gpsAcquired = false;

    while ((millis() - startTime) < 300000) {  // Try for 5 minutes
        if (getGPSLocation()) {
            gpsAcquired = true;
            break;
        }
        delay(5000);  // Wait 5 seconds before retrying
    }

    if (gpsAcquired) {
        Serial.println("✅ GPS Fix Acquired!");
    } else {
        Serial.println("❌ GPS Fix Failed. Continuing without location.");
    }
}


bool getGPSLocation() {
    Serial.println("📡 Checking GPS Data...");
    modem.println("AT+CGNSSINFO");

    long startTime = millis();
    while ((millis() - startTime) < 3000) {  // Wait for response
        while (modem.available()) {
            String response = modem.readString();
            Serial.println("📡 GPS Response: " + response);

            // Ensure the response contains "+CGNSSINFO:" and is not just empty values
            if (isValidGPS(response)) {
                Serial.println("✅ Valid GPS Data Acquired!");
                return true;
            }
        }
    }
    return false;  // No valid GPS data
}


bool isValidGPS(String response) {
    int index = response.indexOf("+CGNSSINFO:");
    if (index == -1) return false;  // Ensure it contains the expected response

    response = response.substring(index + 11);  // Extract GPS data
    response.trim();

    // Check if at least one value is non-empty
    if (response.indexOf(",") == -1) return false;  // Ensure it has commas
    String firstValue = response.substring(0, response.indexOf(","));  // Get first value

    return firstValue.length() > 0;  // If any value exists, it's valid
}

// Function to get GPS data
String getGPSData() {
    Serial.println("Fetching GPS Data...");
    modem.println("AT+CGNSSINFO");
    
    long startTime = millis();
    while ((millis() - startTime) < 3000) {  // Wait for response
        while (modem.available()) {
            String response = modem.readString();
            Serial.println("GPS Response: " + response);
            if (isValidGPS(response)) {
                return response;
            }
        }
    }
    return "N/A"; // Return "N/A" if no valid GPS data is found
}

bool getNetworkTime() {
  Serial.println("GETTING NETWORK TIME!:");
  // Step 1: Enable network time sync (optional but good practice)
  modem.println("AT+CLTS=1");
  modem.println("AT+CTZU=1");
  modem.println("AT+CTZR=1");
  delay(1000);
  while (modem.available()) modem.read();  // Clear buffer

  // Step 2: Try to get the time multiple times
  for (int attempt = 0; attempt < 5; attempt++) {
    modem.println("AT+CCLK?");
    delay(1000);

    String response = "";
    while (modem.available()) {
      response += modem.readStringUntil('\n');
    }

    // Look for CCLK response
    int idx = response.indexOf("+CCLK: ");
    if (idx != -1) {
      int startQuote = response.indexOf('"', idx);
      int endQuote = response.indexOf('"', startQuote + 1);
    
      if (startQuote != -1 && endQuote != -1) {
        String timeStr = response.substring(startQuote + 1, endQuote); // Example: "25/03/13,13:23:59+00"
        Serial.println("📡 Time string: " + timeStr);
    
        // Parse time string
        int yy = timeStr.substring(0, 2).toInt() + 2000;
        int MM = timeStr.substring(3, 5).toInt();
        int dd = timeStr.substring(6, 8).toInt();
        int hh = timeStr.substring(9, 11).toInt();
        int mm = timeStr.substring(12, 14).toInt();
        int ss = timeStr.substring(15, 17).toInt();
    
        // DEBUG: Show parsed values
        Serial.print("🧠 Parsed: ");
        Serial.printf("%04d-%02d-%02d %02d:%02d:%02d\n", yy, MM, dd, hh, mm, ss);
    
        // Convert to Unix timestamp
        tmElements_t tm;
        tm.Year = yy - 1970;  // Offset for TimeLib (not full year)
        tm.Month = MM;
        tm.Day = dd;
        tm.Hour = hh;
        tm.Minute = mm;
        tm.Second = ss;
    
        time_t timestamp = makeTime(tm);
        baseTimestamp = (unsigned long) timestamp;
        baseMillis = millis();
        lastTimeSync = millis();
    
        Serial.println("🕒 Synced Unix timestamp: " + String(baseTimestamp));
        return true; //Successfully got time
      } else {
        Serial.println("❌ Couldn't find CCLK time in quotes.");
      }
    } else {
      Serial.println("❌ +CCLK: not found in response.");
    }

    Serial.println("⚠️ No valid CCLK yet, retrying...");
    delay(2000);
  }

  Serial.println("❌ Failed to retrieve network time.");
  return false;  // ❌ Failed
}

//Returns time in seconds
unsigned long getCurrentTimestamp() {
  Serial.println("Getting current timestamp...");
  return baseTimestamp + ((millis() - baseMillis) / 1000);
}

bool markFileAsSent(String originalFilename) {
  String newFilename = originalFilename;
  newFilename.replace(".txt", "_sent.txt");
  if (SD.rename(originalFilename.c_str(), newFilename.c_str())) {
    Serial.println("Marked file as sent. File renamed to: " + newFilename);
    return true;
  } else {
    Serial.println("Failed to rename file.");
    return false;
  }
}

void sendAllUnsentFiles() {
    File root = SD.open("/");

    String jsonChunk = "{\"data\":[";
    bool firstEntry = true;

    while (true) {
        File file = root.openNextFile();
        if (!file) break;

        String filename = file.name();
        if (filename.startsWith("imu_data") && !filename.endsWith("-sent.txt")) {
            Serial.println("Reading: " + filename);

            String fileContent = readFile(file);
            file.close();

            // Optional: validate JSON
            DynamicJsonDocument temp(2048);
            DeserializationError err = deserializeJson(temp, fileContent);
            if (err) {
                Serial.println("⚠️ Invalid JSON in file: " + filename);
                continue;
            }

            // Sanitize: trim whitespace
            fileContent.trim();

            // Only add comma if it's not the first entry
            if (!firstEntry) {
                jsonChunk += ",";
            }

            jsonChunk += fileContent;
            firstEntry = false;
        }
    }

    jsonChunk += "]}"; // Properly close JSON

    if (!firstEntry) {
        if (sendFileToServer(jsonChunk)) {
            markFilesAsSent();
        } else {
            Serial.println("❌ Failed to send JSON to server.");
        }
    } else {
        Serial.println("✅ No new files to send.");
    }
}

String readFile(File file) {
    String content = "";
    while (file.available()) {
        content += char(file.read());
    }
    content.trim(); // Removes extra newlines or whitespace
    return content;
}

void markFilesAsSent() {
    File root = SD.open("/");

    while (true) {
        File file = root.openNextFile();
        if (!file) break;

        String filename = file.name();
        if (!filename.endsWith("-sent.txt")) {
            String newName = filename;
            newName.replace(".txt", "-sent.txt");
            SD.rename(filename.c_str(), newName.c_str());
            Serial.println("Renamed: " + filename + " -> " + newName);
        }
    }
}
